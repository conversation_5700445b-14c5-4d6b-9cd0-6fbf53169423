// 状态选项
export const statusOptions = [
  { label: "查询", value: "查询" },
  { label: "正式", value: "正式" },
  { label: "流失", value: "流失" },
  { label: "失效", value: "失效" },
];

// 行业类型选项
export const tradeTypeOptions = [
  { label: "传统企业", value: "传统企业" },
  { label: "互联网企业", value: "互联网企业" },
  { label: "XaaS", value: "XaaS" },
  { label: "电信运营商", value: "电信运营商" },
  { label: "转售商", value: "转售商" },
];

// 业务类型选项
export const businessOptions = [
  { label: "NTT业务", value: "NTT业务" },
  { label: "国际业务OB", value: "国际业务OB" },
  { label: "3C业务OB", value: "3C业务OB" },
  { label: "国际业务IB", value: "国际业务IB" },
  { label: "老业务专网", value: "老业务专网" },
  { label: "老业务无线", value: "老业务无线" },
  { label: "老业务语音", value: "老业务语音" },
  { label: "个人语音", value: "个人语音" },
];

// 客户类别选项
export const customerClassOptions = [
  { label: "合同客户", value: "合同客户" },
  { label: "名义客户", value: "名义客户" },
  { label: "最终用户", value: "最终用户" },
];

// 客户类型选项
export const customerTypeOptions = [
  { label: "直客-国内", value: "直客-国内" },
  { label: "直客海外", value: "直客海外" },
  { label: "渠道-国内", value: "渠道-国内" },
  { label: "渠道-海外", value: "渠道-海外" },
];

export const userStatusMap: Record<number, string> = {
  0: "禁用",
  1: "启用",
  2: "冻结",
  3: "注销",
};

export const userStatusSeverityMap: Record<number, string> = {
  0: "success",
  1: "error",
  2: "Warn",
  3: "danger",
};

// 客户联系人类型选项
export const contactClassOptions = [
  { label: "客户", value: "客户" },
  { label: "合同", value: "合同" },
  { label: "账务", value: "账务" },
];

export const contactTypeOptions = [
  { label: "商务", value: "商务" },
  { label: "采购", value: "采购" },
  { label: "账务", value: "账务" },
  { label: "服务", value: "服务" },
  { label: "其他", value: "其他" },
];

export const ownTypeOptions = [
  { label: "客户", value: "客户" },
  { label: "供应商", value: "供应商" },
  { label: "合作渠道", value: "合作渠道" },
];

export const contactStateOptions = [
  { label: "有效", value: "U" },
  { label: "失效", value: "E" },
];

// 合同类型选项
// --------------------------------------------------------------
export const contractTypeOptions = [
  { label: "框架合同", value: "框架合同" },
  { label: "订单合同", value: "订单合同" },
];

export const contractBusinessOptions = [
  { label: "NTT业务", value: "NTT业务" },
  { label: "国际业务IB", value: "国际业务IB" },
  { label: "国际业务OB", value: "国际业务OB" },
  { label: "老业务语音", value: "老业务语音" },
  { label: "老业务专网", value: "老业务专网" },
  { label: "老业务无线", value: "老业务无线" },
  { label: "个人语音", value: "个人语音" },
];

export const contractStateOptions = [
  { label: "合同签署", value: "合同签署" },
  { label: "无合同特批", value: "无合同特批" },
];

export const contractTermOptions = [
  { label: "固定期限合同", value: 0 },
  { label: "无固定期限合同", value: 1 },
];

export const contractTermMap: Record<number, string> = {
  0: "固定期限合同",
  1: "无固定期限合同",
};

// 订单的枚举信息
// --------------------------------------------------------------
// 订单类型 客户正式、客户测试、自用、注销
export const orderTypeOptions = [
  { label: "客户正式", value: "客户正式" },
  { label: "客户测试", value: "客户测试" },
  { label: "自用", value: "自用" },
  { label: "注销", value: "注销" },
];

// 服务类型 新增、变更、续约
export const orderServiceTypeOptions = [
  { label: "新增", value: "新增" },
  { label: "变更", value: "变更" },
  { label: "续约", value: "续约" },
];

// 付费周期 月、季度、年
export const orderPayCycleOptions = [
  { label: "月", value: "月" },
  { label: "季度", value: "季度" },
  { label: "年", value: "年" },
];

// 付费方式 预付、后付、当月付
export const orderPayTypeOptions = [
  { label: "预付", value: "预付" },
  { label: "后付", value: "后付" },
  { label: "当月付", value: "当月付" },
];

// 订单种类 普通-0、语音订单-1、流量订单-2
export const orderClassOptions = [
  { label: "普通", value: 0 },
  { label: "语音订单", value: 1 },
  { label: "流量订单", value: 2 },
];

// 客户状态 查询、正式、流失、失效
export const customerStateSeverityMap: Record<string, string> = {
  查询: "info",
  正式: "success",
  流失: "danger",
  失效: "warn",
};

// 客户审批状态 初始化、待审批、已撤回、已生效、已退回
export const customerApproveStateSeverityMap: Record<string, string> = {
  init: "info",
  confirm: "help",
  revoke: "danger",
  active: "success",
  back: "error",
};

export const customerApproveStateValueMap: Record<string, string> = {
  init: "初始化",
  confirm: "待审批",
  revoke: "已撤回",
  active: "已生效",
  back: "已驳回",
};

// 客户审批操作类型
export const customerApproveActionTypes = {
  SUBMIT: "submit",
  REVOKE: "revoke",
  APPROVE: "approve",
  REJECT: "reject",
};

// 客户审批操作显示文本
export const customerApproveActionLabels = {
  submit: "提交审核",
  revoke: "撤回审核",
  approve: "审核通过",
  reject: "驳回",
};

// 出账类型映射
export const chargeTypeMap: Record<number, string> = {
  1: "系统出账",
  2: "调账",
};

// 调账状态映射
export const adjustStateMap: Record<number, string> = {
  0: "初始化导入",
  1: "审批通过",
  2: "审批拒绝",
};

export const adjustStateSeverityMap: Record<number, string> = {
  0: "info",
  1: "success",
  2: "danger",
};

// 调账类别
export const adjustTypeMap: Record<number, string> = {
  1: "权责调账",
  2: "无权责调账",
  3: "外部费用导入调账",
};

// 调账记录审批操作
export const adjustApproveActionTypes = {
  APPROVE: "approve",
  REJECT: "reject",
};